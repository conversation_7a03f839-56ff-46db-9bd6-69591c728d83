package com.example.habits9.ui.details;

import com.example.habits9.data.analytics.HabitAnalyticsUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class HabitAnalyticsViewModel_Factory implements Factory<HabitAnalyticsViewModel> {
  private final Provider<HabitAnalyticsUseCase> analyticsUseCaseProvider;

  public HabitAnalyticsViewModel_Factory(Provider<HabitAnalyticsUseCase> analyticsUseCaseProvider) {
    this.analyticsUseCaseProvider = analyticsUseCaseProvider;
  }

  @Override
  public HabitAnalyticsViewModel get() {
    return newInstance(analyticsUseCaseProvider.get());
  }

  public static HabitAnalyticsViewModel_Factory create(
      Provider<HabitAnalyticsUseCase> analyticsUseCaseProvider) {
    return new HabitAnalyticsViewModel_Factory(analyticsUseCaseProvider);
  }

  public static HabitAnalyticsViewModel newInstance(HabitAnalyticsUseCase analyticsUseCase) {
    return new HabitAnalyticsViewModel(analyticsUseCase);
  }
}
