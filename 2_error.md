> Task :app:compileDebugKotlin
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/data/analytics/AnalyticsVerification.kt:173:13 No parameter with name 'habitType' found.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/data/analytics/AnalyticsVerification.kt:185:13 No parameter with name 'habitType' found.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/data/analytics/AnalyticsVerification.kt:186:13 No parameter with name 'numericalHabitType' found.

> Task :app:compileDebugKotlin FAILED

FAILURE: Build failed with an exception.

* What went wrong:
Execution failed for task ':app:compileDebugKotlin'.
> A failure occurred while executing org.jetbrains.kotlin.compilerRunner.GradleCompilerRunnerWithWorkers$GradleKotlinCompilerWorkAction
   > Compilation error. See log for more details

* Try:
> Run with --stacktrace option to get the stack trace.
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
> Get more help at https://help.gradle.org.

BUILD FAILED in 28s
30 actionable tasks: 2 executed, 4 from cache, 24 up-to-date
