# Prompt 5.1: Implement Analytics Data Logic for Individual Habits

## A. The Objective & Context

The goal of this task is to create all the underlying business logic required to calculate and expose analytics for a **single habit**. This is the first of a two-part implementation; we are focusing **only on the data layer** (ViewModels, Repositories, Use Cases). No UI should be built in this task.

**CRITICAL**: Before writing new code, you must first investigate the existing codebase. If any of the required calculations (like streak counting or completion rates) are already implemented elsewhere in the project, reuse that logic to ensure consistency and avoid duplication.

The implementation must support two distinct types of habits:
1.  **Yes/No Habits**: Standard completion tracking.
2.  **Measurable Habits**: Tracking with a numerical value (e.g., "Run 5 km").

The logic you build will need to provide all the data points detailed below.

## B. Detailed Implementation Plan

### 1. Create Data Models for Analytics
- Create new Kotlin `data class` structures to hold the calculated metrics. This will ensure the data passed to the UI layer is clean and well-organized.
  - `YesNoHabitAnalytics`
  - `MeasurableHabitAnalytics`

### 2. Implement General Information Logic
- The ViewModel associated with the Habit Details screen must expose the following:
  - **Current Week**: A string formatted as `W` followed by the week number (e.g., "W34").
  - **Current Date**: A string formatted with the abbreviated day and date (e.g., "Sat, 09 Aug").

### 3. Implement Core Metrics Logic (For BOTH Habit Types)
- This logic should be placed in a repository or a dedicated use case/interactor class.
- **`getCurrentStreak(habitId)`**:
  - Calculate the number of consecutive days a habit was completed, ending today.
  - The logic must iterate backward from today, checking that the habit was both **completed** and **scheduled** for each day. The streak breaks on the first day that was scheduled but not completed.
- **`getLongestStreak(habitId)`**:
  - Calculate the longest streak of consecutive completions in the habit's entire history.
  - This requires iterating through all historical records for the habit.
- **`getCompletionRate(habitId)`**:
  - Calculate the overall completion percentage.
  - The formula is: `(Total Completions / Total Scheduled Days) * 100`.
- **`getTotalCompletions(habitId)`**:
  - Calculate the total number of times the habit has been marked as completed.

### 4. Implement Measurable Habit Metrics Logic
- This logic should extend or supplement the core metrics for measurable habits.
- **`getTotalAmount(habitId)`**:
  - Calculate the sum of all numerical values recorded for the habit.
- **`getAveragePerCompletion(habitId)`**:
  - Calculate the average value per completion. The formula is: `Total Amount / Total Completions`.
- **`getBestDay(habitId)`**:
  - Find the single highest numerical value ever recorded for the habit.

### 5. Implement Bar Chart Data Logic ("Completion History")
- Create a function `getCompletionHistory(habitId, timePeriod: TimePeriod)` where `TimePeriod` is an enum (`WEEK`, `MONTH`, `QUARTER`, `YEAR`).
- This function should return a list of data points suitable for a bar chart (e.g., `List<Pair<String, Float>>` where the first value is the label and the second is the value).
- **For Yes/No Habits**: The value should be the **count** of completions within each period.
- **For Measurable Habits**: The value should be the **sum** of the recorded amounts within each period.

### 6. Implement Calendar Data Logic ("Heatmap")
- Create a function `getCalendarData(habitId)` that returns data for the last 6 months.
- The return type should be a map where the key is the date and the value indicates the status.
- **For Yes/No Habits**: Return a `Map<LocalDate, Boolean>` where `true` means completed.
- **For Measurable Habits**: Return a `Map<LocalDate, Float>` where the `Float` is the numerical value recorded for that day. This will allow the UI to create a graded color scale.

## C. Meticulous Verification Plan

1.  **General Info Verification**:
    - **CRITICAL**: Verify that the functions for the current week and date return the correct strings in the specified format (e.g., "W34", "Sat, 09 Aug").

2.  **Core Metrics Verification**:
    - Create a test habit and add entries for the last 10 days, but miss day 8. Verify `getCurrentStreak` returns `7`.
    - Add a separate streak of 15 days earlier in the month. Verify `getLongestStreak` returns `15`.
    - If the habit was scheduled for 30 days and completed on 25, verify `getCompletionRate` returns `83.3%`.
    - Verify `getTotalCompletions` returns the correct count.

3.  **Measurable Metrics Verification**:
    - Create a test measurable habit (e.g., "Pages Read"). Add entries: Day 1 (20), Day 2 (30), Day 3 (25).
    - **CRITICAL**: Verify `getTotalAmount` returns `75`.
    - Verify `getAveragePerCompletion` returns `25`.
    - Verify `getBestDay` returns `30`.

4.  **Chart Data Verification**:
    - Add test data spanning multiple weeks and months.
    - Call `getCompletionHistory` with `TimePeriod.MONTH`. Verify the returned list contains the correct completion counts (or summed values) for each month.
    - Call `getCalendarData`. **CRITICAL**: Verify the returned map contains exactly one entry for each of the last ~180 days and that the values (Boolean or Float) accurately reflect the test data.

## D. Mandatory Development Guidelines

These practices must be followed during all phases of development—planning, implementation, and review.

### 1. Refer to the Style Guide
Before starting any feature:
- Always consult the **style guide** for rules related to UI/UX, layout, naming conventions, spacing, colors, and design patterns.
- The style guide is located in the **`style.md` file in the root folder`**.
- It is the **single source of truth** for styling decisions.

### 2. Study the Reference Project
Prior to implementation:
- Review the **reference project** located in the `uhabits-dev` folder (in the root directory).
- Understand how similar features have been approached to maintain consistency and avoid duplications or contradictions.
- The reference project serves as a **blueprint** for implementation.
- This step is mandatory. **Do not proceed to implementation without this step.**

### 3. Understand the Existing Project Structure
Before writing any code:
- Spend time exploring and understanding how the current system is structured.
- Even for new features, existing components or utility functions may be reusable.
- Integrate changes **cleanly into the existing architecture** instead of creating disconnected code.

### 4. Maintain a Clean Codebase
After implementing features:
- Remove any temporary, test, or duplicate files, folders, routes, or unused components that were created during development.
- Keep the codebase **organized and clutter-free**.

### 5. Pause If There Is Any Confusion
If at any point the requirements are unclear:
- **Do not proceed** based on assumptions.
- Immediately pause and seek clarification either from the project lead or directly from me.
- It is better to get clarity than to redo or fix avoidable mistakes later.

### 6. Remove Unused Old Implementations
As part of final review:
- Identify and delete **any old, unused code** that was implemented earlier but is no longer in use.
- This includes obsolete routes, modules, features, or legacy logic.