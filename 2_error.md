 Task :app:compileDebugKotlin FAILED
w: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/habits9/ui/details/HabitDetailsScreen.kt:84:57 '@property:Deprecated(...) val Icons.Filled.ArrowBack: ImageVector' is deprecated. Use the AutoMirrored version at Icons.AutoMirrored.Filled.ArrowBack.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/uhabits_99/MainActivity.kt:94:29 No value passed for parameter 'habitId'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/uhabits_99/MainActivity.kt:94:29 No value passed for parameter 'habitName'.
e: file:///E:/Habit_Tracker_WorkSpace/UHabits_99/app/src/main/java/com/example/uhabits_99/MainActivity.kt:94:29 No value passed for parameter 'habitType'.

FAILURE: Build failed with an exception.

* What went wrong:
Execution failed for task ':app:compileDebugKotlin'.
> A failure occurred while executing org.jetbrains.kotlin.compilerRunner.GradleCompilerRunnerWithWorkers$GradleKotlinCompilerWorkAction
   > Compilation error. See log for more details

* Try:
> Run with --stacktrace option to get the stack trace.
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
> Get more help at https://help.gradle.org.

BUILD FAILED in 8s
30 actionable tasks: 2 executed, 28 up-to-date
